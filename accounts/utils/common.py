import hashlib
import random
from myoperator.centrallog import config
import re
import time
import uuid as u
from typing import Any
from django.core.cache import cache
from django.utils import timezone
from dateutil import tz
import pytz


def uuid():
    uniqid = (
        hex(int(time.time()))[2:10]
        + hex(int(time.time() * 1000000) % 0x100000)[2:7]
    )
    rand = random.randint(111, 999)
    return str(uniqid) + str(rand)


def get_aadhaar_hash(aadhaar_no):
    return hashlib.md5(aadhaar_no.encode()).hexdigest()


def duration_to_seconds(duration):
    if not re.match(r"^(\d+d)?(\d+h)?(\d+m)?(\d+s)?$", duration):
        raise ValueError("Invalid duration format")

    days = hours = minutes = seconds = 0
    if "d" in duration:
        days, duration = duration.split("d")
    if "h" in duration:
        hours, duration = duration.split("h")
    if "m" in duration:
        minutes, duration = duration.split("m")
    if "s" in duration:
        seconds, duration = duration.split("s")
    return (
        int(days) * 86400 + int(hours) * 3600 + int(minutes) * 60 + int(seconds)
    )


def seconds_to_duration(seconds):
    duration = ""
    if seconds is None:
        return None
    days, seconds = divmod(seconds, 86400)
    hours, seconds = divmod(seconds, 3600)
    minutes, seconds = divmod(seconds, 60)

    if days > 0:
        duration += f"{days}d"
    if hours > 0:
        duration += f"{hours}h"
    if minutes > 0:
        duration += f"{minutes}m"
    if seconds > 0:
        duration += f"{seconds}s"

    return duration


def get_cache(key: str) -> Any:
    return cache.get(key)


def set_cache(key: str, value: Any, timeout: int = 3600) -> None:
    cache.set(key, value, timeout=timeout)


def invalidate_cache(key: str) -> None:
    cache.delete(key)


def convert_utc_to_local(
    date_time: timezone.datetime, time_zone: str
) -> timezone.datetime:
    tzone = tz.gettz(time_zone)
    local_datetime = date_time.replace(tzinfo=tz.gettz("UTC")).astimezone(tzone)
    return local_datetime


def convert_to_utc(
    local_datetime: timezone.datetime, time_zone: str
) -> timezone.datetime:
    # Convert the local datetime to a timezone-aware datetime with the specified time zone
    local_timezone = pytz.timezone(time_zone)
    local_datetime = local_timezone.localize(
        local_datetime
    )  # Make it timezone-aware if not already

    # Convert the local datetime to UTC
    utc_datetime = local_datetime.astimezone(pytz.UTC)

    return utc_datetime


def mask_number(number: str, mask_length: int = 3) -> str:
    if not number:
        return ""
    if len(number) <= mask_length:
        return "*" * len(number)
    return number[-mask_length:].rjust(len(number), "*")


def get_trace_id() -> str:
    """Returns UUID configured for CentralFormatter or generates a new one.

    Returns:
        str: The UUID.
    """

    cfg = config.get_config()
    trace_id = (cfg.uid if cfg else None) or u.uuid4()

    return str(trace_id)
