from django.core.files.uploadedfile import UploadedFile
import json
import logging
import typing as t

from rest_framework import status

from accounts.billing_accounts.exceptions import InvalidGstNumberException
from accounts.billing_accounts.utils.gst_parser import SurepassGSTParser
from accounts.exceptions import OTPMaxAttemptException
from accounts.kyc.cache_handler import GstCacheHandler
from accounts.kyc.exceptions import (
    AadhaarSendOtpApiException,
    AadhaarVerifyOtpApiException,
    KycFileDownloadException,
    PanOcrApiException,
)
from accounts.kyc.parsers import (
    AadhaarFileDownloadParser,
    AadhaarParser,
    GstFileDownloadParser,
    PanOCRParser,
)
from accounts.kyc.schemas import (
    AadhaarDetail,
    AadhaarSendOtpApiResponseData,
    PanDetail,
)

from .base import SurepassBase

logger = logging.getLogger(__name__)


if t.TYPE_CHECKING:
    from accounts.billing_accounts.utils.gst_parser import GSTDetail


class SurepassApi(SurepassBase):
    def gstin_details_from_api(self, gst_number: str) -> "GSTDetail":
        url = f"{self.HOST}/api/v1/corporate/gstin-advanced"
        data = json.dumps({"id_number": gst_number})
        response = self.post_json(url, data=data, timeout=self.TIMEOUT)
        response_json = json.loads(response.text)
        if response.status_code == status.HTTP_200_OK:
            return SurepassGSTParser(response_json["data"]).parse()

        message = response_json.get("message", "Invalid GSTIN number.")
        raise InvalidGstNumberException(message)

    def gstin_details(self, gst_number: str):
        gst_cache_handler = GstCacheHandler(gst_number)
        cached_data = gst_cache_handler.get()
        if cached_data:
            logger.info("Using cached GST data")
            logger.debug(cached_data)
            try:
                return SurepassGSTParser(cached_data).parse()
            except Exception as e:
                logger.error(
                    f"Error parsing cached GST data: {e}", exc_info=True
                )

        logger.info("Fetching GST data from API")
        gst_data: GSTDetail = SurepassApi().gstin_details_from_api(gst_number)
        logger.debug(gst_data.data)
        gst_cache_handler.set(gst_data)
        return gst_data

    def send_otp_for_aadhaar(
        self, aadhaar_number: str
    ) -> AadhaarSendOtpApiResponseData:
        """Sends OTP for Aadhaar number verification.

        Args:
            aadhaar_number (str): The Aadhaar number to send OTP for.

        Raises:
            AadhaarSendOtpApiException: If the API call fails or the response is invalid.
            OTPMaxAttemptException: If the API returns 429 status code.

        Returns:
            AadhaarSendOtpApiResponseData: The parsed response data.
        """

        url = f"{self.HOST}/api/v1/aadhaar-v2/generate-otp"
        data = json.dumps({"id_number": aadhaar_number})

        response = self.post_json(url=url, data=data, timeout=self.TIMEOUT)

        try:
            response_json = response.json()
        except json.JSONDecodeError as e:
            error_msg = "Failed to parse Surepass Aadhaar send OTP API response as JSON."
            logger.critical(
                error_msg,
                title="Surepass Aadhaar send OTP API JSONDecodeError exc",
                exc_info=True,
            )
            raise AadhaarSendOtpApiException(error_msg) from e

        if response.status_code == status.HTTP_429_TOO_MANY_REQUESTS:
            raise OTPMaxAttemptException(
                message="Maximum attempts reached. Please try again later or use GST-based authentication."
            )

        response_json_data = response_json.get("data")

        if not response_json_data:
            error_msg = (
                "No data field in Surepass Aadhaar send OTP API response: %s"
                % response_json
            )
            logger.error(
                error_msg,
                title="Surepass Aadhaar send OTP API no data field",
                exc_info=True,
            )
            raise AadhaarSendOtpApiException(
                response_json.get("message") or error_msg
            )

        aadhaar_send_otp_api_response_data = AadhaarSendOtpApiResponseData(
            **response_json_data
        )

        if (
            response.status_code == status.HTTP_200_OK
            and aadhaar_send_otp_api_response_data.is_successful()
        ):
            logger.info(
                "OTP sent successfully on the phone number linked to aadhaar_number: %s"
                % aadhaar_number,
                title="Surepass Aadhaar send OTP API success",
            )
            return aadhaar_send_otp_api_response_data

        failure_reason = aadhaar_send_otp_api_response_data.get_failure_reason()
        logger.critical(
            "Failed to send OTP for aadhaar_number: %s, reason: %s Surepass Aadhaar send OTP API invalid response: %s"
            % (
                aadhaar_number,
                failure_reason,
                response_json,
            ),
            title="Surepass Aadhaar send OTP API failure",
            exc_info=True,
        )
        raise AadhaarSendOtpApiException(
            "Failed to send Aadhaar OTP. %s" % failure_reason
        )

    def verify_otp_for_aadhaar(
        self, aadhaar_client_id: str, aadhaar_otp: str
    ) -> AadhaarDetail:
        """Verifies OTP for Aadhaar number verification.

        Args:
            aadhaar_client_id (str): The client id returned by the send OTP API.
            aadhaar_otp (str): The OTP sent to the phone number linked to the Aadhaar number.

        Raises:
            AadhaarVerifyOtpApiException: If the API call fails or the response is invalid.
            OTPMaxAttemptException: If the API returns 429 status code.

        Returns:
            AadhaarDetail: The parsed response data.
        """

        url = f"{self.HOST}/api/v1/aadhaar-v2/submit-otp"
        data = json.dumps({"client_id": aadhaar_client_id, "otp": aadhaar_otp})

        response = self.post_json(url=url, data=data, timeout=self.TIMEOUT)

        try:
            response_json = response.json()
        except json.JSONDecodeError as e:
            error_msg = "Failed to parse Surepass Aadhaar verify OTP API response as JSON."
            logger.critical(
                error_msg,
                title="Surepass Aadhaar verify OTP API JSONDecodeError exc",
                exc_info=True,
            )
            raise AadhaarVerifyOtpApiException(error_msg) from e

        if response.status_code == status.HTTP_429_TOO_MANY_REQUESTS:
            raise OTPMaxAttemptException(
                message="Maximum attempts reached. Please try again later or use GST-based authentication."
            )

        if response.status_code != status.HTTP_200_OK:
            error_msg = (
                "Surepass Aadhaar verify OTP API invalid response: %s"
                % response_json
            )
            logger.critical(
                error_msg,
                title="Surepass Aadhaar verify OTP API failure",
                exc_info=True,
            )
            raise AadhaarVerifyOtpApiException(
                response_json.get("message") or error_msg
            )

        response_json_data = response_json.get("data")

        if not response_json_data:
            error_msg = (
                "No data field in Surepass Aadhaar verify OTP API response: %s"
                % response_json
            )
            logger.error(
                error_msg,
                title="Surepass Aadhaar verify OTP API no data field",
                exc_info=True,
            )
            raise AadhaarVerifyOtpApiException(
                response_json.get("message") or error_msg
            )

        aadhaar_detail: AadhaarDetail = AadhaarParser().parse(
            data=response_json_data
        )

        if response.status_code == status.HTTP_200_OK and aadhaar_detail:
            logger.info(
                "OTP verified successfully.",
                title="Surepass Aadhaar verify OTP API success",
            )
            return aadhaar_detail

        logger.critical(
            "Failed to verify Aadhaar OTP. Surepass Aadhaar verify OTP API invalid response: %s"
            % response_json,
            title="Surepass Aadhaar verify OTP API failure",
            exc_info=True,
        )
        raise AadhaarVerifyOtpApiException(
            "Failed to verify Aadhaar OTP. Surepass Aadhaar verify OTP API invalid response."
        )

    def download_gst_pdf(self, gst_number: str) -> GstFileDownloadParser:
        url = f"{self.HOST}/api/v1/corporate/gstin-pdf-report"
        data = json.dumps({"id_number": gst_number})
        try:
            response = self.post_json(url, data=data, timeout=self.TIMEOUT)
        except json.JSONDecodeError as e:
            error_msg = "Failed to parse Surepass GST PDF download API response as JSON."
            logger.critical(
                error_msg,
                title="Surepass GST PDF download API JSONDecodeError exc",
                exc_info=True,
            )
            raise KycFileDownloadException(error_msg) from e

        if response.status_code == status.HTTP_200_OK:
            return GstFileDownloadParser(response.json()).parse()
        raise KycFileDownloadException(
            "Failed to download GST PDF: %s" % response.text
        )

    def download_aadhaar_pdf(
        self, aadhaar_client_id: str
    ) -> AadhaarFileDownloadParser:
        """Downloads Aadhaar PDF from Surepass API.

        Args:
            aadhaar_client_id (str): The client id returned by the send OTP API.

        Raises:
            KycFileDownloadException: If the API call fails or the response is invalid.

        Returns:
            AadhaarFileDownloadParser: The parsed response data.
        """

        url = f"{self.HOST}/api/v1/aadhaar-v2/generate-pdf"
        data = json.dumps({"client_id": aadhaar_client_id})

        response = self.post_json(url=url, data=data, timeout=self.TIMEOUT)

        try:
            response_json = response.json()
        except json.JSONDecodeError as e:
            error_msg = "Failed to parse Surepass Aadhaar PDF download API response as JSON."
            logger.critical(
                error_msg,
                title="Surepass Aadhaar PDF download API JSONDecodeError exc",
                exc_info=True,
            )
            raise KycFileDownloadException(error_msg) from e

        if response.status_code == status.HTTP_200_OK:
            return AadhaarFileDownloadParser(data=response_json).parse()

        error_msg = "Failed to download Aadhaar PDF."
        logger.critical(
            error_msg,
            title="Surepass Aadhaar PDF download API failure",
            exc_info=True,
        )
        raise KycFileDownloadException(error_msg)

    def fetch_pan_info_from_file(self, pan_file: UploadedFile) -> PanDetail:
        """Fetches PAN info from file using Surepass PAN OCR API.

        Args:
            pan_file (UploadedFile): The PAN file to fetch info from.

        Raises:
            PanOcrApiException: If the API call fails or the response is invalid.

        Returns:
            PanDetail: The parsed response data.
        """

        url = f"{self.HOST}/api/v1/ocr/pan"
        files = {"file": (pan_file.name, pan_file, pan_file.content_type)}

        response = self.post_multipart_form_data(
            url=url, files=files, timeout=self.TIMEOUT
        )

        try:
            response_json = response.json()
        except json.JSONDecodeError as e:
            error_msg = "Failed to parse Surepass PAN OCR API response as JSON."
            logger.critical(
                error_msg,
                title="Surepass PAN OCR API JSONDecodeError exc",
                exc_info=True,
            )
            raise PanOcrApiException(error_msg) from e

        if response.status_code == status.HTTP_200_OK:
            return PanOCRParser(response_json).parse()

        error_msg = (
            "Failed to fetch PAN info from file using Surepass PAN OCR API."
        )
        logger.critical(
            f"{error_msg} Response: {response_json}",
            title="Surepass PAN OCR API failure",
            exc_info=True,
        )
        raise PanOcrApiException(error_msg)
