SERVER_ERROR = "ACC_0001"
VALIDATION_ERROR = "ACC_0002"

# Billing Account
# -------------------------------------------------------------
ALREADY_MARKED_AS_FRAUD = "ACC_0110"
FRAUD_BILLING_ACCOUNT = "ACC_0111"

# Offers
# -------------------------------------------------------------
OFFER_EXPIRED = "ACC_0201"
OFFER_ALREADY_APPLIED = "ACC_0202"

# Verification
# -------------------------------------------------------------
VERIFICATION_RULE_CONFLICT = "ACC_0301"


# Cafs
# --------------------------------------------------------------
CAF_NOT_APPROVED = "ACC_0401"

# Resource Charge
# --------------------------------------------------------------
RESOURCE_CHARGE_INSUFFICIENT_BALANCE = "ACC_4010"
RESOURCE_CHARGE_ALREADY_EXISTS = "ACC_4011"

ERRORS = [
    (SERVER_ERROR, "Server Error"),
    (
        VALIDATION_ERROR,
        "Validation Failed, Please check errors field for details",
    ),
]
