import typing as t
from accounts.core.constants import MODULE_CONFIG
import time


class PresignedUrlInputValidator:
    def __init__(
        self,
        module_string: str,
        content_type: str,
        file_name: t.Optional[str] = None,
    ):
        self.module, self.submodule = self._parse_module_string(module_string)
        self.config = self._get_config()
        self.content_type = content_type
        self.file_name = file_name
        self.final_file_name = None
        self.file_size = self.config["DEFAULT_FILE_UPLOAD_MAX_SIZE_IN_BYTES"]

    def validate(self):
        """Performs all validations and prepares final values."""
        self._validate_content_type()
        # self._validate_file_size()
        self._handle_file_name()

    def _parse_module_string(self, module_string: str) -> t.<PERSON><PERSON>[str, str]:
        """Parses the module string and returns the module and submodule.

        The module string is expected to be in the format 'module_submodule' (e.g., 'kyc_pan').

        Args:
            module_string (str): The module string to be parsed.

        Raises:
            ValueError: If the module string is not in the expected format.

        Returns:
            t.<PERSON>[str, str]: A tuple containing the module and submodule.
        """

        try:
            module, sub_module = module_string.lower().split("_", 1)
            return module, sub_module
        except ValueError:
            raise ValueError(
                f"Invalid module format '{module_string}'. Expected format: 'module_submodule' (e.g., 'kyc_pan')"
            )

    def _get_config(self) -> t.Dict[str, t.Any]:
        """Fetches the config for the module and submodule.

        Raises:
            ValueError: If the module or submodule is not supported.

        Returns:
            t.Dict[str, t.Any]: A dictionary containing the config for the module and submodule.
        """

        if self.module not in MODULE_CONFIG:
            raise ValueError(f"Unsupported module '{self.module}'")
        if self.submodule not in MODULE_CONFIG[self.module]:
            raise ValueError(
                f"Unsupported submodule '{self.submodule}' for module '{self.module}'"
            )
        return MODULE_CONFIG[self.module][self.submodule]

    def _validate_content_type(self):
        """Validates the content type.

        Raises:
            ValueError: If the content type is not supported.
        """

        if self.content_type not in self.config["ALLOWED_CONTENT_TYPES"]:
            raise ValueError(
                f"Invalid content_type: '{self.content_type}'. Allowed values: {self.config['ALLOWED_CONTENT_TYPES']}"
            )

    # def _validate_file_size(self):
    #     """Validates the file size.

    #     Raises:
    #         ValueError: If the file size exceeds the allowed limit.
    #     """

    #     if self.file_size > self.default_file_size:
    #         raise ValueError(
    #             f"File size {self.file_size}MB exceeds the allowed limit of {self.default_file_size}MB"
    #         )

    def _handle_file_name(self):
        """Handles the file name.

        If the file name is not provided, a default name is generated using the current timestamp.
        If the file name is provided, it is validated to ensure it ends with the correct extension.

        Raises:
            ValueError: If the file name is provided but does not end with the correct extension.
        """

        ext = self.get_file_extension()

        if self.file_name:
            if not self.file_name.lower().endswith(ext):
                raise ValueError(
                    f"File name must end with {ext} for content_type: '{self.content_type}'"
                )
            self.final_file_name = self.file_name
        else:
            if isinstance(ext, tuple):
                ext = ext[0]
            self.final_file_name = f"{int(time.time())}.{ext}"

    def get_final_file_name(self) -> str:
        return self.final_file_name  # type: ignore

    def get_s3_prefix(self) -> str:
        return self.config["S3_PREFIX"]

    def get_file_extension(self) -> t.Union[str, t.Tuple[str]]:
        try:
            return {
                "image/jpeg": ("jpeg", "jpg"),
                "image/png": "png",
            }[self.content_type]
        except KeyError:
            raise ValueError(f"Unsupported content_type '{self.content_type}'")
