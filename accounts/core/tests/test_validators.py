import time
from unittest.mock import patch

import pytest

from accounts.core.validators import PresignedUrlInputValidator


class TestPresignedUrlInputValidator:
    """Test cases for PresignedUrlInputValidator class."""

    def test_init_valid_module_string(self):
        """Test initialization with valid module string."""
        validator = PresignedUrlInputValidator(
            module_string="kyc_pan",
            content_type="image/jpeg"
        )

        assert validator.module == "kyc"
        assert validator.submodule == "pan"
        assert validator.content_type == "image/jpeg"
        assert validator.file_name is None
        assert validator.final_file_name is None
        assert validator.file_size == validator.config["DEFAULT_FILE_UPLOAD_MAX_SIZE_IN_BYTES"]

    def test_init_with_file_name(self):
        """Test initialization with file name."""
        validator = PresignedUrlInputValidator(
            module_string="kyc_pan",
            content_type="image/png",
            file_name="test.png"
        )

        assert validator.file_name == "test.png"

    def test_parse_module_string_valid(self):
        """Test _parse_module_string with valid input."""
        validator = PresignedUrlInputValidator(
            module_string="kyc_pan",
            content_type="image/jpeg"
        )

        module, submodule = validator._parse_module_string("kyc_pan")
        assert module == "kyc"
        assert submodule == "pan"

    def test_parse_module_string_case_insensitive(self):
        """Test _parse_module_string is case insensitive."""
        validator = PresignedUrlInputValidator(
            module_string="KYC_PAN",
            content_type="image/jpeg"
        )

        module, submodule = validator._parse_module_string("KYC_PAN")
        assert module == "kyc"
        assert submodule == "pan"

    def test_parse_module_string_with_multiple_underscores(self):
        """Test _parse_module_string with multiple underscores."""
        validator = PresignedUrlInputValidator(
            module_string="kyc_pan",
            content_type="image/jpeg"
        )

        module, submodule = validator._parse_module_string("kyc_pan_document")
        assert module == "kyc"
        assert submodule == "pan_document"

    def test_parse_module_string_invalid_format(self):
        """Test _parse_module_string with invalid format."""
        with pytest.raises(ValueError, match="Invalid module format 'invalid'. Expected format: 'module_submodule'"):
            PresignedUrlInputValidator(
                module_string="invalid",
                content_type="image/jpeg"
            )

    def test_parse_module_string_empty(self):
        """Test _parse_module_string with empty string."""
        with pytest.raises(ValueError, match="Invalid module format ''. Expected format: 'module_submodule'"):
            PresignedUrlInputValidator(
                module_string="",
                content_type="image/jpeg"
            )

    def test_get_config_valid_module_submodule(self):
        """Test _get_config with valid module and submodule."""
        validator = PresignedUrlInputValidator(
            module_string="kyc_pan",
            content_type="image/jpeg"
        )

        config = validator._get_config()
        assert "ALLOWED_CONTENT_TYPES" in config
        assert "DEFAULT_FILE_UPLOAD_MAX_SIZE_IN_BYTES" in config
        assert "S3_PREFIX" in config

    def test_get_config_invalid_module(self):
        """Test _get_config with invalid module."""
        with pytest.raises(ValueError, match="Unsupported module 'invalid'"):
            PresignedUrlInputValidator(
                module_string="invalid_submodule",
                content_type="image/jpeg"
            )

    def test_get_config_invalid_submodule(self):
        """Test _get_config with invalid submodule."""
        with pytest.raises(ValueError, match="Unsupported submodule 'invalid' for module 'kyc'"):
            PresignedUrlInputValidator(
                module_string="kyc_invalid",
                content_type="image/jpeg"
            )

    def test_validate_content_type_valid_jpeg(self):
        """Test _validate_content_type with valid JPEG content type."""
        validator = PresignedUrlInputValidator(
            module_string="kyc_pan",
            content_type="image/jpeg"
        )

        # Should not raise any exception
        validator._validate_content_type()

    def test_validate_content_type_valid_png(self):
        """Test _validate_content_type with valid PNG content type."""
        validator = PresignedUrlInputValidator(
            module_string="kyc_pan",
            content_type="image/png"
        )

        # Should not raise any exception
        validator._validate_content_type()

    def test_validate_content_type_invalid(self):
        """Test _validate_content_type with invalid content type."""
        validator = PresignedUrlInputValidator(
            module_string="kyc_pan",
            content_type="image/gif"
        )

        with pytest.raises(ValueError, match="Invalid content_type: 'image/gif'. Allowed values:"):
            validator._validate_content_type()

    def test_get_file_extension_jpeg(self):
        """Test get_file_extension for JPEG content type."""
        validator = PresignedUrlInputValidator(
            module_string="kyc_pan",
            content_type="image/jpeg"
        )

        extension = validator.get_file_extension()
        assert extension == ("jpeg", "jpg")

    def test_get_file_extension_png(self):
        """Test get_file_extension for PNG content type."""
        validator = PresignedUrlInputValidator(
            module_string="kyc_pan",
            content_type="image/png"
        )

        extension = validator.get_file_extension()
        assert extension == "png"

    def test_get_file_extension_unsupported(self):
        """Test get_file_extension for unsupported content type."""
        validator = PresignedUrlInputValidator(
            module_string="kyc_pan",
            content_type="image/gif"
        )

        with pytest.raises(ValueError, match="Unsupported content_type 'image/gif'"):
            validator.get_file_extension()

    @patch('time.time', return_value=1234567890)
    def test_handle_file_name_without_provided_name_jpeg(self, mock_time):
        """Test _handle_file_name without provided file name for JPEG."""
        validator = PresignedUrlInputValidator(
            module_string="kyc_pan",
            content_type="image/jpeg"
        )

        validator._handle_file_name()
        assert validator.final_file_name == "1234567890.jpeg"

    @patch('time.time', return_value=1234567890)
    def test_handle_file_name_without_provided_name_png(self, mock_time):
        """Test _handle_file_name without provided file name for PNG."""
        validator = PresignedUrlInputValidator(
            module_string="kyc_pan",
            content_type="image/png"
        )

        validator._handle_file_name()
        assert validator.final_file_name == "1234567890.png"