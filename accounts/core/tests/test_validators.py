from unittest.mock import patch

import pytest

from accounts.core.validators import PresignedUrlInputValidator


# Test initialization and basic functionality
def test_presigned_url_input_validator_init_valid():
    """Test successful initialization with valid parameters."""
    validator = PresignedUrlInputValidator(
        module_string="kyc_pan",
        content_type="image/jpeg"
    )

    assert validator.module == "kyc"
    assert validator.submodule == "pan"
    assert validator.content_type == "image/jpeg"
    assert validator.file_name is None
    assert validator.final_file_name is None
    assert validator.file_size == validator.config["DEFAULT_FILE_UPLOAD_MAX_SIZE_IN_BYTES"]


def test_presigned_url_input_validator_init_with_file_name():
    """Test initialization with file name provided."""
    validator = PresignedUrlInputValidator(
        module_string="kyc_pan",
        content_type="image/png",
        file_name="test_document.png"
    )

    assert validator.file_name == "test_document.png"
    assert validator.content_type == "image/png"


def test_presigned_url_input_validator_init_invalid_module_format():
    """Test initialization with invalid module string format."""
    with pytest.raises(ValueError, match="Invalid module format 'invalid'. Expected format: 'module_submodule'"):
        PresignedUrlInputValidator(
            module_string="invalid",
            content_type="image/jpeg"
        )


def test_presigned_url_input_validator_init_empty_module_string():
    """Test initialization with empty module string."""
    with pytest.raises(ValueError, match="Invalid module format ''. Expected format: 'module_submodule'"):
        PresignedUrlInputValidator(
            module_string="",
            content_type="image/jpeg"
        )


def test_presigned_url_input_validator_init_unsupported_module():
    """Test initialization with unsupported module."""
    with pytest.raises(ValueError, match="Unsupported module 'invalid'"):
        PresignedUrlInputValidator(
            module_string="invalid_submodule",
            content_type="image/jpeg"
        )


def test_presigned_url_input_validator_init_unsupported_submodule():
    """Test initialization with unsupported submodule."""
    with pytest.raises(ValueError, match="Unsupported submodule 'invalid' for module 'kyc'"):
        PresignedUrlInputValidator(
            module_string="kyc_invalid",
            content_type="image/jpeg"
        )


# Test _parse_module_string method
def test_parse_module_string_valid():
    """Test _parse_module_string with valid input."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg")

    module, submodule = validator._parse_module_string("kyc_pan")
    assert module == "kyc"
    assert submodule == "pan"


def test_parse_module_string_case_insensitive():
    """Test _parse_module_string handles case conversion."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg")

    module, submodule = validator._parse_module_string("KYC_PAN")
    assert module == "kyc"
    assert submodule == "pan"


def test_parse_module_string_multiple_underscores():
    """Test _parse_module_string with multiple underscores."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg")

    module, submodule = validator._parse_module_string("kyc_pan_document")
    assert module == "kyc"
    assert submodule == "pan_document"


def test_parse_module_string_no_underscore():
    """Test _parse_module_string with no underscore."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg")

    with pytest.raises(ValueError, match="Invalid module format 'invalid'. Expected format: 'module_submodule'"):
        validator._parse_module_string("invalid")


# Test _get_config method
def test_get_config_valid():
    """Test _get_config with valid module and submodule."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg")

    config = validator._get_config()
    assert "ALLOWED_CONTENT_TYPES" in config
    assert "DEFAULT_FILE_UPLOAD_MAX_SIZE_IN_BYTES" in config
    assert "S3_PREFIX" in config


def test_get_config_invalid_module():
    """Test _get_config with invalid module."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg")
    validator.module = "invalid"

    with pytest.raises(ValueError, match="Unsupported module 'invalid'"):
        validator._get_config()


def test_get_config_invalid_submodule():
    """Test _get_config with invalid submodule."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg")
    validator.submodule = "invalid"

    with pytest.raises(ValueError, match="Unsupported submodule 'invalid' for module 'kyc'"):
        validator._get_config()


# Test _validate_content_type method
def test_validate_content_type_valid_jpeg():
    """Test _validate_content_type with valid JPEG content type."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg")

    # Should not raise any exception
    validator._validate_content_type()


def test_validate_content_type_valid_png():
    """Test _validate_content_type with valid PNG content type."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/png")

    # Should not raise any exception
    validator._validate_content_type()


def test_validate_content_type_invalid():
    """Test _validate_content_type with invalid content type."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg")
    validator.content_type = "image/gif"

    with pytest.raises(ValueError, match="Invalid content_type: 'image/gif'. Allowed values:"):
        validator._validate_content_type()


def test_validate_content_type_invalid_text():
    """Test _validate_content_type with text content type."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg")
    validator.content_type = "text/plain"

    with pytest.raises(ValueError, match="Invalid content_type: 'text/plain'. Allowed values:"):
        validator._validate_content_type()


# Test get_file_extension method
def test_get_file_extension_jpeg():
    """Test get_file_extension for JPEG content type."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg")

    extension = validator.get_file_extension()
    assert extension == ("jpeg", "jpg")


def test_get_file_extension_png():
    """Test get_file_extension for PNG content type."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/png")

    extension = validator.get_file_extension()
    assert extension == "png"


def test_get_file_extension_unsupported():
    """Test get_file_extension for unsupported content type."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg")
    validator.content_type = "image/gif"

    with pytest.raises(ValueError, match="Unsupported content_type 'image/gif'"):
        validator.get_file_extension()


# Test _handle_file_name method
@patch('time.time', return_value=1234567890)
def test_handle_file_name_without_provided_name_jpeg(mock_time):
    """Test _handle_file_name without provided file name for JPEG."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg")

    validator._handle_file_name()
    assert validator.final_file_name == "1234567890.jpeg"


@patch('time.time', return_value=1234567890)
def test_handle_file_name_without_provided_name_png(mock_time):
    """Test _handle_file_name without provided file name for PNG."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/png")

    validator._handle_file_name()
    assert validator.final_file_name == "1234567890.png"


def test_handle_file_name_with_valid_jpeg_extension():
    """Test _handle_file_name with valid JPEG file name."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg", "document.jpeg")

    validator._handle_file_name()
    assert validator.final_file_name == "document.jpeg"


def test_handle_file_name_with_valid_jpg_extension():
    """Test _handle_file_name with valid JPG file name."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg", "document.jpg")

    validator._handle_file_name()
    assert validator.final_file_name == "document.jpg"


def test_handle_file_name_with_valid_png_extension():
    """Test _handle_file_name with valid PNG file name."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/png", "document.png")

    validator._handle_file_name()
    assert validator.final_file_name == "document.png"


def test_handle_file_name_case_insensitive_extension():
    """Test _handle_file_name with case insensitive extension matching."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg", "document.JPEG")

    validator._handle_file_name()
    assert validator.final_file_name == "document.JPEG"


def test_handle_file_name_invalid_extension_for_jpeg():
    """Test _handle_file_name with invalid extension for JPEG content type."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg", "document.png")

    with pytest.raises(ValueError, match="File name must end with \\('jpeg', 'jpg'\\) for content_type: 'image/jpeg'"):
        validator._handle_file_name()


def test_handle_file_name_invalid_extension_for_png():
    """Test _handle_file_name with invalid extension for PNG content type."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/png", "document.jpg")

    with pytest.raises(ValueError, match="File name must end with png for content_type: 'image/png'"):
        validator._handle_file_name()


def test_handle_file_name_no_extension():
    """Test _handle_file_name with file name without extension."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg", "document")

    with pytest.raises(ValueError, match="File name must end with \\('jpeg', 'jpg'\\) for content_type: 'image/jpeg'"):
        validator._handle_file_name()


# Test get_final_file_name method
def test_get_final_file_name_after_validation():
    """Test get_final_file_name after validation."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg", "test.jpg")
    validator._handle_file_name()

    assert validator.get_final_file_name() == "test.jpg"


@patch('time.time', return_value=9876543210)
def test_get_final_file_name_generated(mock_time):
    """Test get_final_file_name with generated file name."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/png")
    validator._handle_file_name()

    assert validator.get_final_file_name() == "9876543210.png"


# Test get_s3_prefix method
def test_get_s3_prefix():
    """Test get_s3_prefix returns correct prefix."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg")

    assert validator.get_s3_prefix() == "companydoc/"


# Test validate method (integration test)
def test_validate_complete_flow_with_file_name():
    """Test complete validation flow with provided file name."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg", "document.jpg")

    validator.validate()

    assert validator.final_file_name == "document.jpg"


@patch('time.time', return_value=1111111111)
def test_validate_complete_flow_without_file_name(mock_time):
    """Test complete validation flow without provided file name."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/png")

    validator.validate()

    assert validator.final_file_name == "1111111111.png"


def test_validate_complete_flow_invalid_content_type():
    """Test complete validation flow with invalid content type."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg")
    validator.content_type = "application/pdf"

    with pytest.raises(ValueError, match="Invalid content_type: 'application/pdf'. Allowed values:"):
        validator.validate()


def test_validate_complete_flow_invalid_file_extension():
    """Test complete validation flow with invalid file extension."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/png", "document.gif")

    with pytest.raises(ValueError, match="File name must end with png for content_type: 'image/png'"):
        validator.validate()


# Additional edge case tests
def test_module_string_with_spaces():
    """Test module string with spaces (should fail)."""
    with pytest.raises(ValueError, match="Invalid module format 'kyc pan'. Expected format: 'module_submodule'"):
        PresignedUrlInputValidator("kyc pan", "image/jpeg")


def test_module_string_with_special_characters():
    """Test module string with special characters (should fail)."""
    with pytest.raises(ValueError, match="Invalid module format 'kyc@pan'. Expected format: 'module_submodule'"):
        PresignedUrlInputValidator("kyc@pan", "image/jpeg")


def test_file_name_with_path():
    """Test file name with path components."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg", "folder/document.jpg")

    validator._handle_file_name()
    assert validator.final_file_name == "folder/document.jpg"


def test_file_name_empty_string():
    """Test file name as empty string (should be treated as None)."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg", "")

    # Empty string should be treated as no file name provided
    with patch('time.time', return_value=5555555555):
        validator._handle_file_name()
        assert validator.final_file_name == "5555555555.jpeg"


def test_content_type_case_sensitivity():
    """Test content type case sensitivity."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg")
    validator.content_type = "IMAGE/JPEG"

    with pytest.raises(ValueError, match="Invalid content_type: 'IMAGE/JPEG'. Allowed values:"):
        validator._validate_content_type()


def test_file_extension_matching_case_insensitive():
    """Test file extension matching is case insensitive."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/png", "document.PNG")

    validator._handle_file_name()
    assert validator.final_file_name == "document.PNG"


def test_file_name_with_multiple_dots():
    """Test file name with multiple dots."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg", "my.document.v1.jpg")

    validator._handle_file_name()
    assert validator.final_file_name == "my.document.v1.jpg"


def test_very_long_file_name():
    """Test very long file name."""
    long_name = "a" * 200 + ".png"
    validator = PresignedUrlInputValidator("kyc_pan", "image/png", long_name)

    validator._handle_file_name()
    assert validator.final_file_name == long_name


def test_file_name_with_unicode_characters():
    """Test file name with unicode characters."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg", "документ.jpg")

    validator._handle_file_name()
    assert validator.final_file_name == "документ.jpg"


def test_get_final_file_name_before_validation():
    """Test get_final_file_name before validation (should return None)."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg")

    # Before validation, final_file_name should be None
    assert validator.get_final_file_name() is None


def test_validator_state_after_initialization():
    """Test validator state immediately after initialization."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/png", "test.png")

    assert validator.module == "kyc"
    assert validator.submodule == "pan"
    assert validator.content_type == "image/png"
    assert validator.file_name == "test.png"
    assert validator.final_file_name is None
    assert isinstance(validator.file_size, int)
    assert validator.file_size > 0


def test_config_structure():
    """Test that config has required keys."""
    validator = PresignedUrlInputValidator("kyc_pan", "image/jpeg")

    config = validator.config
    required_keys = ["ALLOWED_CONTENT_TYPES", "DEFAULT_FILE_UPLOAD_MAX_SIZE_IN_BYTES", "S3_PREFIX"]

    for key in required_keys:
        assert key in config

    assert isinstance(config["ALLOWED_CONTENT_TYPES"], (list, tuple))
    assert isinstance(config["DEFAULT_FILE_UPLOAD_MAX_SIZE_IN_BYTES"], int)
    assert isinstance(config["S3_PREFIX"], str)