from django.core.files.uploadedfile import UploadedFile
import logging
import typing as t

from django.db import transaction
from statemachine import State, StateMachine

from accounts.kyc.enums import (
    KYCModeEnum,
    KYCStateEnum,
    KYCStateStatusEnum,
)
from accounts.kyc.tasks import (
    download_gst_pdf_task,
    fetch_gst_info_for_kyc_task,
    fetch_pan_info_from_file_task,
)
from accounts.kyc.models import KYC


logger = logging.getLogger(__name__)


class KYCStateMachine(StateMachine):
    # states
    verification = State(value=KYCStateEnum.VERIFICATION.value, initial=True)
    gst_info = State(value=KYCStateEnum.GST_INFO.value)
    upload_pan = State(value=KYCStateEnum.UPLOAD_PAN.value)
    digilocker_pan = State(value=KYCStateEnum.DIGILOCKER_PAN.value)
    video_kyc = State(value=KYCStateEnum.VIDEO_KYC.value)
    e_sign = State(value=KYCStateEnum.E_SIGN.value)
    kyc_done = State(value=KYCStateEnum.KYC_DONE.value, final=True)

    # transitions
    # via GST mode
    verification_to_video_kyc = verification.to(
        video_kyc, cond=["is_kyc_mode_gst", "is_verification_completed"]
    )

    # via Aadhaar mode
    verification_to_gst_info = verification.to(
        gst_info, cond=["is_kyc_mode_aadhaar", "is_verification_completed"]
    )
    verification_to_upload_pan = verification.to(
        upload_pan, cond=["is_kyc_mode_aadhaar", "is_verification_completed"]
    )
    verification_to_digilocker_pan = verification.to(
        digilocker_pan,
        cond=["is_kyc_mode_aadhaar", "is_verification_completed"],
    )

    gst_info_to_video_kyc = gst_info.to(
        video_kyc, cond=["is_kyc_mode_aadhaar", "is_gst_info_completed"]
    )
    upload_pan_to_video_kyc = upload_pan.to(
        video_kyc, cond=["is_kyc_mode_aadhaar", "is_upload_pan_completed"]
    )
    digilocker_pan_to_video_kyc = digilocker_pan.to(
        video_kyc, cond=["is_kyc_mode_aadhaar", "is_digilocker_pan_completed"]
    )

    video_kyc_to_e_sign = video_kyc.to(
        e_sign,
        cond=["not is_e_sign_skipped", "is_video_kyc_completed"],
    )
    e_sign_to_kyc_done = e_sign.to(kyc_done, cond=["is_e_sign_completed"])
    video_kyc_to_kyc_done = video_kyc.to(
        kyc_done,
        cond=["is_e_sign_skipped", "is_video_kyc_completed"],
    )

    def __init__(
        self,
        initial_state: t.Optional[str] = None,
        kyc_mode: KYCModeEnum = KYCModeEnum.GST,
    ):
        self.kyc_mode = kyc_mode
        super().__init__()

        if initial_state:
            self.current_state = self.get_state_by_value(initial_state)

    def get_state_by_value(self, state_value: str):
        """Fetches a state by its value.

        Args:
            state_value (str): The value of the state to fetch.

        Raises:
            ValueError: If no state with the given value is found.
        """

        for state in self.states:
            if state.value == state_value:
                return state

        error_msg = (
            "[KYCStateMachine] No KYC state found with value: %s. Failed to instantiate KYCStateMachine."
            % state_value
        )
        logger.critical(error_msg, title="KYC state not found", exc_info=True)
        raise ValueError(error_msg)

    def get_states_mapping(self):
        """Returns a mapping of state values.

        Returns:
            _type_: _description_
        """

        return {
            self.verification.value: {
                KYCModeEnum.GST.value: self.video_kyc.value,
                KYCModeEnum.AADHAAR.value: {
                    self.gst_info.value: self.video_kyc.value,
                    self.upload_pan.value: self.video_kyc.value,
                    self.digilocker_pan.value: self.video_kyc.value,
                },
            },
            self.video_kyc.value: self.e_sign.value,
            self.e_sign.value: self.kyc_done.value,
            self.kyc_done.value: None,
        }

    def is_kyc_mode_gst(self) -> bool:
        return self.kyc_mode == KYCModeEnum.GST

    def is_kyc_mode_aadhaar(self) -> bool:
        return self.kyc_mode == KYCModeEnum.AADHAAR

    def is_verification_completed(self) -> bool:
        # Override this method to implement the actual logic
        # For example: Determine from the Database
        return True

    def is_gst_info_completed(self) -> bool:
        # Override this method to implement the actual logic
        # For example: Determine from the Database
        return True

    def is_upload_pan_completed(self) -> bool:
        # Override this method to implement the actual logic
        # For example: Determine from the Database
        return True

    def is_digilocker_pan_completed(self) -> bool:
        # Override this method to implement the actual logic
        # For example: Determine from the Database
        return True

    def is_video_kyc_completed(self) -> bool:
        # Override this method to implement the actual logic
        # For example: Determine from the Database
        return True

    def is_e_sign_completed(self) -> bool:
        # Override this method to implement the actual logic
        # For example: Determine from the Database
        return True

    def is_kyc_done_completed(self) -> bool:
        # Override this method to implement the actual logic
        # For example: Determine from the Database
        return True

    def is_e_sign_skipped(self) -> bool:
        # Override this method to implement the actual logic
        # Future provision: This is a placeholder for any logic that determines if e_sign is skipped
        return False


class PersistedKYCStateMachine(KYCStateMachine):
    def __init__(self, kyc: KYC):
        self.kyc = kyc
        super().__init__(
            initial_state=self.kyc.current_state,
            kyc_mode=KYCModeEnum(self.kyc.mode),
        )

    @transaction.atomic
    def on_verification_to_gst_info(self, gst_number):
        """
        This method updates the KYC state to GST_INFO, creates a new state
        record, and schedules the fetch_gst_info_for_kyc_task to be executed
        asynchronously. It also sets the task ID in the latest state record.

        Args:
            gst_number (str): The GST number to fetch the data for.

        Returns:
            str: The task ID of the task that was created.
        """
        self.kyc.current_state = KYCStateEnum.GST_INFO.value
        self.kyc.save()
        self.kyc.create_kyc_state(data={})

        task = fetch_gst_info_for_kyc_task.apply_async(  # type: ignore
            args=[gst_number, self.kyc.pk],
            link=download_gst_pdf_task.si(gst_number, self.kyc.pk),  # type: ignore
        )
        self.kyc.get_latest_state().update_task_id(task.id)
        logger.info(
            f"[on_verification_to_gst_info] Scheduled fetch_gst_info_for_kyc_task for GST number: {gst_number} with task id: {task.id}"
        )
        return task.id

    def after_verification_to_gst_info(self):
        """
        Marks the latest KYC state as completed.
        """
        self.kyc.mark_latest_state_as_completed(KYCStateEnum.GST_INFO)

    @transaction.atomic
    def on_verification_to_upload_pan(self, pan_file: UploadedFile) -> str:
        self.kyc.current_state = KYCStateEnum.UPLOAD_PAN.value
        self.kyc.save()
        self.kyc.create_kyc_state(data={})

        task = fetch_pan_info_from_file_task.apply_async(  # type: ignore
            args=[pan_file, self.kyc.pk],
        )
        self.kyc.get_latest_state().update_task_id(task.id)
        logger.info(
            f"[on_verification_to_upload_pan] Scheduled fetch_pan_info_from_file_task task id: {task.id}"
        )
        return task.id

    def after_verification_to_upload_pan(self):
        self.kyc.mark_latest_state_as_completed(KYCStateEnum.UPLOAD_PAN)

    def is_verification_completed(self) -> bool:
        """Checks if the verification state is completed.

        Returns:
            bool: True if the verification state is completed, False otherwise.
        """

        kyc_state = self.kyc.get_latest_state(state=KYCStateEnum.VERIFICATION)

        return kyc_state.data not in (None, {}) and kyc_state.is_completed()

    def is_gst_info_completed(self) -> bool:
        """Checks if the gst_info state is completed.

        Returns:
            bool: True if the gst_info state is completed, False otherwise.
        """

        kyc_state = self.kyc.get_latest_state(state=KYCStateEnum.GST_INFO)

        return (kyc_state is not None) and (
            kyc_state.status == KYCStateStatusEnum.COMPLETED.value
        )

    def is_upload_pan_completed(self) -> bool:
        """Checks if the upload_pan state is completed.

        Returns:
            bool: True if the upload_pan state is completed, False otherwise.
        """

        kyc_state = self.kyc.get_latest_state(state=KYCStateEnum.UPLOAD_PAN)

        return (kyc_state is not None) and (
            kyc_state.status == KYCStateStatusEnum.COMPLETED.value
        )

    def is_digilocker_pan_completed(self) -> bool:
        """Checks if the digilocker_pan state is completed.

        Returns:
            bool: True if the digilocker_pan state is completed, False otherwise.
        """

        kyc_state = self.kyc.get_latest_state(state=KYCStateEnum.DIGILOCKER_PAN)

        return (kyc_state is not None) and (
            kyc_state.status == KYCStateStatusEnum.COMPLETED.value
        )

    def is_video_kyc_completed(self) -> bool:
        """Checks if the video_kyc state is completed.

        Returns:
            bool: True if the video_kyc state is completed, False otherwise.
        """

        kyc_state = self.kyc.get_latest_state(state=KYCStateEnum.VIDEO_KYC)

        return (kyc_state is not None) and (
            kyc_state.status == KYCStateStatusEnum.COMPLETED.value
        )

    def is_e_sign_completed(self) -> bool:
        """Checks if the e_sign state is completed.

        Returns:
            bool: True if the e_sign state is completed, False otherwise.
        """

        kyc_state = self.kyc.get_latest_state(state=KYCStateEnum.E_SIGN)

        return (kyc_state is not None) and (
            kyc_state.status == KYCStateStatusEnum.COMPLETED.value
        )

    def is_kyc_done_completed(self) -> bool:
        """Checks if the kyc_done state is completed.

        Returns:
            bool: True if the kyc_done state is completed, False otherwise.
        """

        kyc_state = self.kyc.get_latest_state(state=KYCStateEnum.E_SIGN)

        return (kyc_state is not None) and (
            kyc_state.status == KYCStateStatusEnum.COMPLETED.value
        )

    def is_marked_as_failed(self) -> bool:
        """Checks if the KYC is marked as failed.

        Returns:
            bool: True if the KYC is marked as failed, False otherwise.
        """

        return self.kyc.is_marked_as_failed()

    def mark_as_failed(self, failure_reason: str):
        """Marks the KYC as failed and updates the latest KYC state as failed with the provided reason.

        Args:
            failure_reason (str): The reason for marking the KYC as failed.

        Raises:
            Exception: If an error occurs while marking the KYC as failed.
        """

        self.kyc.mark_as_failed(failure_reason=failure_reason)
