import logging
import typing as t

from django.conf import settings
from django.core.files.uploadedfile import UploadedFile

from rest_framework import status
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON>, MultiPartParser
from rest_framework.response import Response
from rest_framework.settings import api_settings
from rest_framework.views import APIView
from statemachine.exceptions import TransitionNotAllowed

from accounts.exceptions import (
    BaseException,
)
from accounts.kyc.exceptions import (
    InvalidPanUploadException,
    TransitionNotAllowedException,
)
from accounts.kyc.mixins import KycObjMixin
from accounts.kyc.models import KYC
from accounts.kyc.sm.statemachines import PersistedKYCStateMachine
from accounts.utils.permissions.billing_account import BillingAccountPermission

logger = logging.getLogger(__name__)


class PanUploadView(APIView, KycObjMixin):
    permission_classes = [BillingAccountPermission]
    parser_classes = list(api_settings.DEFAULT_PARSER_CLASSES) + [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser]  # type: ignore

    def validate_pan_upload_request(self, pan_file: t.Optional[UploadedFile]):
        if not pan_file:
            raise InvalidPanUploadException()

        if pan_file.content_type not in settings.PAN["ALLOWED_CONTENT_TYPES"]:
            raise InvalidPanUploadException(
                f"Invalid file content type. Allowed content types: {settings.PAN['ALLOWED_CONTENT_TYPES']}."
            )

        if not pan_file.name.lower().endswith(
            settings.PAN["ALLOWED_FILE_EXTENSIONS"]
        ):
            raise InvalidPanUploadException(
                f"Invalid file extension. Allowed extensions: {settings.PAN['ALLOWED_FILE_EXTENSIONS']}"
            )

    def transition(self, kyc: KYC, pan_file: UploadedFile):
        sm = PersistedKYCStateMachine(kyc)
        try:
            task_id = sm.verification_to_upload_pan(pan_file)
            return task_id
        except TransitionNotAllowed as error:
            logger.error(
                f"Transition not allowed for kyc_id: {kyc.id}, error: {error}",
                exc_info=True,
            )
            raise TransitionNotAllowedException() from error

    def post(self, request, *args, **kwargs):
        try:
            self.validate_pan_upload_request(request.data.get("file"))
            pan_file = request.data["file"]
            kyc = self.get_object(request)
            task_id = self.transition(kyc, pan_file)
            return Response(
                {
                    "status": "success",
                    "code": status.HTTP_200_OK,
                    "message": "PAN upload process started successfully.",
                    "data": {"task_id": task_id},
                },
                status=status.HTTP_200_OK,
            )
        except BaseException as error:
            return Response(
                {
                    "status": "error",
                    "code": error.get_error_code(),
                    "message": str(error),
                    "errors": error.get_errors(),
                },
                status=error.get_http_status_code(),
            )
