from dataclasses import asdict
from django.core.files.uploadedfile import UploadedFile
import logging

import celery

from accounts.billing_accounts.enums import DocTypeEnums
from accounts.kyc.enums import KYCStateEnum
from accounts.kyc.exceptions import KycNotFoundException
from accounts.kyc.handlers import <PERSON>ycHand<PERSON>
from accounts.kyc.models import KYC
from accounts.kyc.parsers import (
    AadhaarFileDownloadParser,
    GstFileDownloadParser,
)
from accounts.utils.api_services.surepass import SurepassApi
from accounts.exceptions import BaseException
from accounts.utils.api_services.surepass_helpers import get_gst_data
from kyc.schemas import PanDetail

logger = logging.getLogger(__name__)


@celery.shared_task(bind=True)
def download_gst_pdf_task(self, gst_number: str, kyc_id: str):
    """Downloads GST PDF and creates a document for the KYC.

    Args:
        gst_number (str): The GST number.
        kyc_id (str): The KYC id.

    Raises:
        KycNotFoundException: If the KYC object does not exist.
        BaseException: If any error occurs.
    """

    logger.info(
        f"[download_gst_pdf_task] starting task request task_id: {self.request.id}, kyc_id: {kyc_id}, gst_number: {gst_number}"
    )
    self.update_state(state=celery.states.STARTED)  # type: ignore
    try:
        kyc = KYC.get_object(kyc_id)
    except KycNotFoundException as e:
        logger.critical(
            f"[download_gst_pdf_task] KYC not found for kyc_id: {kyc_id}, error: {e}",
            exc_info=True,
        )
        self.update_state(state=celery.states.FAILURE)  # type: ignore
        raise e

    try:
        api_data: GstFileDownloadParser = SurepassApi().download_gst_pdf(
            gst_number=gst_number
        )
        kyc_handler = KycHandler(kyc)
        kyc_handler.process_file(
            file_url=api_data.pdf_url,
            resource_number=gst_number,
            doc_type=DocTypeEnums.GST,
        )
        kyc_handler.kyc.mark_latest_state_as_completed()
        self.update_state(state=celery.states.SUCCESS)  # type: ignore
    except BaseException as e:
        error_msg = "%s: %s" % (e.__class__.__name__, str(e))
        kyc.mark_as_failed("%s, trace_id: %s" % (error_msg, e.get_trace_id()))
        logger.critical(
            "[download_gst_pdf_task] KYC GST PDF Download Failed for gst_number: %s, error: %s"
            % (gst_number, error_msg),
            exc_info=True,
        )
        self.update_state(state=celery.states.FAILURE)  # type: ignore
        raise e


@celery.shared_task(bind=True)
def download_aadhaar_pdf_task(
    self, aadhaar_number: str, aadhaar_client_id: str, kyc_id: str
):
    """Downloads Aadhaar PDF and creates a document for the KYC.

    Args:
        aadhaar_number (str): The Aadhaar number.
        aadhaar_client_id (str): The Aadhaar client id.
        kyc_id (str): The KYC id.

    Raises:
        KycNotFoundException: If the KYC object does not exist.
        BaseException: If any error occurs.
    """

    logger.info(
        "[download_aadhaar_pdf_task] starting task request task_id: %s, kyc_id: %s, aadhaar_number: %s, aadhaar_client_id: %s"
        % (
            self.request.id,
            kyc_id,
            aadhaar_number,
            aadhaar_client_id,
        )
    )
    self.update_state(state=celery.states.STARTED)  # type: ignore

    try:
        kyc = KYC.get_object(kyc_id)
    except KycNotFoundException as e:
        logger.critical(
            f"[download_aadhaar_pdf_task] KYC not found for kyc_id: {kyc_id}, error: {e}",
            title="KYC not found",
            exc_info=True,
        )
        self.update_state(state=celery.states.FAILURE)  # type: ignore
        raise e

    try:
        api_data: (
            AadhaarFileDownloadParser
        ) = SurepassApi().download_aadhaar_pdf(
            aadhaar_client_id=aadhaar_client_id
        )
        kyc_handler = KycHandler(kyc)
        kyc_handler.process_file(
            file_url=api_data.pdf_url,
            resource_number=aadhaar_number,
            doc_type=DocTypeEnums.AADHAAR_CARD,
        )
        kyc_handler.kyc.mark_latest_state_as_completed(
            state=KYCStateEnum.VERIFICATION
        )
        self.update_state(state=celery.states.SUCCESS)  # type: ignore
    except BaseException as e:
        error_msg = "%s: %s" % (e.__class__.__name__, str(e))
        kyc.mark_as_failed("%s, trace_id: %s" % (error_msg, e.get_trace_id()))
        logger.critical(
            "[download_aadhaar_pdf_task] KYC Aadhaar PDF Download Failed for aadhaar_number: %s, error: %s"
            % (aadhaar_number, error_msg),
            title="KYC Aadhaar PDF Download Failed",
            exc_info=True,
        )
        self.update_state(state=celery.states.FAILURE)  # type: ignore
        raise e


@celery.shared_task(bind=True)
def fetch_gst_info_for_kyc_task(self, gst_number: str, kyc_id: str):
    """Fetches GST info and updates the KYC state.

    Args:
        gst_number (str): The GST number.
        kyc_id (str): The KYC id.

    Raises:
        KycNotFoundException: If the KYC object does not exist.
        BaseException: If any error occurs.
    """

    logger.info(
        "[fetch_gst_info_for_kyc_task] starting task request kyc_id: {kyc_id}, gst_number: {gst_number}"
    )
    self.update_state(state=celery.states.STARTED)  # type: ignore
    try:
        kyc = KYC.get_object(kyc_id)
    except KycNotFoundException as e:
        logger.critical(
            f"[fetch_gst_info_for_kyc_task] KYC not found for kyc_id: {kyc_id}, error: {e}",
            title="KYC not found",
            exc_info=True,
        )
        self.update_state(state=celery.states.FAILURE)  # type: ignore
        raise e

    try:
        gst_data = get_gst_data(gst_number)
        kyc.get_latest_state().update_data(gst_data.data)
        self.update_state(state=celery.states.SUCCESS)  # type: ignore

    except BaseException as e:
        error_msg = "%s: %s" % (e.__class__.__name__, str(e))
        logger.critical(
            f"[fetch_gst_info_for_kyc_task] KYC GST Info Fetch Failed for gst_number: {gst_number}, error: {error_msg}",
            exc_info=True,
        )
        self.update_state(state=celery.states.FAILURE)  # type: ignore
        kyc.mark_as_failed(f"{error_msg}, trace_id: {e.get_trace_id()}")
        raise e


@celery.shared_task(bind=True)
def fetch_pan_info_from_file_task(self, pan_file: UploadedFile, kyc_id: str):
    logger.info(
        f"[fetch_pan_info_from_file_task] starting task request task_id: {self.request.id}, kyc_id: {kyc_id}"
    )
    self.update_state(state=celery.states.STARTED)  # type: ignore
    try:
        kyc = KYC.get_object(kyc_id)
    except KycNotFoundException as e:
        logger.critical(
            f"[fetch_pan_info_from_file_task] KYC not found for kyc_id: {kyc_id}, error: {e}",
            title="KYC not found",
            exc_info=True,
        )
        self.update_state(state=celery.states.FAILURE)  # type: ignore
        raise e
    try:
        pan_detail: PanDetail = SurepassApi().fetch_pan_info_from_file(
            pan_file=pan_file
        )
        kyc.get_latest_state().update_data(asdict(pan_detail))
        self.update_state(state=celery.states.SUCCESS)  # type: ignore
    except BaseException as e:
        error_msg = "%s: %s" % (e.__class__.__name__, str(e))
        logger.critical(
            f"[fetch_pan_info_from_file_task] KYC PAN Info Fetch Failed for kyc_id: {kyc_id}, error: {error_msg}",
            exc_info=True,
        )
        self.update_state(state=celery.states.FAILURE)  # type: ignore
        kyc.mark_as_failed(f"{error_msg}, trace_id: {e.get_trace_id()}")
        raise e
