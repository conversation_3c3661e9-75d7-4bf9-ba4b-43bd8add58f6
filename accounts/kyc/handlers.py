from datetime import datetime
import logging
import io
import typing as t

from django.conf import settings

from accounts.billing_accounts.models import BillingAccountDocs, BillingAccounts
from accounts.kyc.exceptions import KycFailedException
from accounts.kyc.models import KYC
from accounts.utils.api_services.file_service import FileService
from accounts.utils.aws.s3 import S3Client
from urllib.parse import urlparse
from accounts.billing_accounts.enums import DocTypeEnums
from accounts.kyc.enums import KYCModeEnum

logger = logging.getLogger(__name__)


class KycHandler:
    FILE_DOWNLOAD_TIMEOUT = 10  # todo: fetch from settings
    S3_PREFIX = "companydoc"

    def __init__(self, kyc: KYC):
        self.kyc = kyc
        self.file_service = FileService()
        self.s3_client = S3Client(settings.AWS_STORAGE_BUCKET_NAME)

    @classmethod
    def init_gst_kyc(
        cls, billing_account: BillingAccounts, gst_data: t.Dict
    ) -> KYC:
        """Initializes a GST KYC.

        Args:
            billing_account (BillingAccounts): The billing account.

        Returns:
            KYC: The initialized KYC.
        """

        logger.info(
            f"[KycHandler] Initializing GST KYC for billing account (id: {billing_account.id})"
        )

        kyc = KYC.initialize_kyc(
            mode=KYCModeEnum.GST,
            billing_account=billing_account,
            data=gst_data,
        )
        return kyc

    @classmethod
    def init_aadhaar_kyc(
        cls, billing_account: BillingAccounts, aadhaar_data: t.Dict
    ) -> KYC:
        """Initializes Aadhaar KYC.

        Args:
            billing_account (BillingAccounts): The billing account.

        Returns:
            KYC: The initialized KYC.
        """

        logger.info(
            f"[KycHandler] Initializing Aadhaar KYC for billing account (id: {billing_account.id})"
        )

        kyc = KYC.initialize_kyc(
            mode=KYCModeEnum.AADHAAR,
            billing_account=billing_account,
            data=aadhaar_data,
        )
        return kyc

    def get_file_name(self, url: str) -> str:
        parsed_url = urlparse(url)
        path = parsed_url.path
        file_name = path.split("/")[-1]
        return file_name

    def upload_file_to_s3(self, file_data: io.BytesIO, extension: str):
        epoch = str(int(datetime.now().timestamp()))
        key = f"{self.S3_PREFIX}/{epoch}.{extension}"
        try:
            self.s3_client.upload_file_obj(file_data, key)
        except Exception as err:
            raise KycFailedException(message="S3 File Upload Failed.") from err
        return key

    def create_doc(
        self,
        doc_name: str,
        doc_ext: str,
        s3_path: str,
        doc_type: DocTypeEnums,
        doc_number: t.Optional[str] = None,
    ) -> BillingAccountDocs:
        return BillingAccountDocs.create_doc(
            self.kyc.billing_account,
            self.kyc,
            s3_path,
            doc_type,
            doc_name,
            doc_ext,
            doc_number,
        )

    def download_file_from_url(
        self,
        url: str,
        timeout: t.Optional[int] = FILE_DOWNLOAD_TIMEOUT,
        output_path: t.Optional[str] = None,
    ) -> t.Union[str, io.BytesIO]:
        """
        Downloads a file from the given URL and saves it to the specified output path or returns it in memory.

        Args:
            url (str): The URL to download the file from.
            timeout (t.Optional[int], optional): The timeout for the request in seconds. Defaults to FILE_DOWNLOAD_TIMEOUT.
            output_path (t.Optional[str], optional): The path where the file should be saved. If None, the file is returned in memory. Defaults to None.

        Returns:
            t.Union[str, io.BytesIO]: The path to the saved file if output_path is provided, otherwise a BytesIO object containing the file content.
        """

        logger.info(
            f"[KycHandler] Downloading file from URL: {url} with timeout: {timeout} and output path: {output_path}"
        )

        response = self.file_service.get_file_from_url(url=url, timeout=timeout)

        if output_path:
            with open(output_path, "wb") as f:
                f.write(response.content)
            return output_path
        return io.BytesIO(response.content)

    def process_file(
        self, file_url: str, resource_number: str, doc_type: DocTypeEnums
    ):
        """Downloads the file from the given URL, uploads it to S3 and creates a document for the KYC.

        Args:
            file_url (str): The URL to download the file from.
            resource_number (str): The resource number.
            doc_type (DocTypeEnums): The type of document.
        """
        logger.info(
            f"[KycHandler] Processing file for resource_number: {resource_number}, doc_type: {doc_type}, file_url: {file_url}"
        )

        file_data = self.download_file_from_url(url=file_url)
        file_name_with_ext = self.get_file_name(url=file_url)
        file_name, extension = file_name_with_ext.split(".")
        logger.info(
            f"[KycHandler] Downloaded PDF for resource_number: {resource_number}, file_name_with_ext: {file_name_with_ext}"
        )
        s3_path = self.upload_file_to_s3(
            file_data=file_data, extension=extension
        )
        self.create_doc(
            file_name,
            extension,
            s3_path,
            doc_type,
            doc_number=resource_number,
        )
