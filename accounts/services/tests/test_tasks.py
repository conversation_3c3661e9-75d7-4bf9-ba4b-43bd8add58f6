import celery
from unittest import mock
from django.test import TestCase, override_settings
from accounts.services.tasks import (
    service_activation_task,
    refund_pending_resource_charges,
)
from accounts.payments.tests.factories import (
    RechargesFactory,
    TrackingSettlementHistoriesFactory,
    PaymentTracksFactory,
)
from accounts.services.tests.factories import ServiceFactory
from accounts.billing_accounts.tests.factories import (
    BillingAccountFactory,
    BillingAccountCreditsFactory,
)
from accounts.services.tests.factories import ResourceChargeFactory
from accounts.services.models import ResourceCharges
from accounts.billing_accounts.models import BillingAccountCredits
from accounts.exceptions import ChatAPIException
import pytest


@pytest.mark.skip(reason="Skipping as celery tests are failing")
class TestServiceActivationTask(TestCase):
    def setUp(self):
        self.recharge = RechargesFactory.create(
            payment_id="abc_123_333", request_of="activation"
        )
        self.service = ServiceFactory.create(
            billing_account=self.recharge.billing_account,
        )
        self.payment_track = PaymentTracksFactory.create(
            payment_id=self.recharge.payment_id, amount=self.recharge.amount
        )
        self.payment_settlement_history = (
            TrackingSettlementHistoriesFactory.create(
                billing_account=self.recharge.billing_account,
                setl_key=self.payment_track.txn_setl_key,
                payment=self.payment_track,
                amount=self.payment_track.amount,
            )
        )

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @mock.patch("accounts.services.tasks.service_activation")
    def test_service_activation_task_success(self, service_activation):
        service_activation.return_value = True
        ban_id = self.recharge.billing_account.id
        payment_id = self.recharge.payment_id

        task = service_activation_task.apply([ban_id, payment_id])
        self.assertEqual(task.status, celery.states.SUCCESS)

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    def test_service_activation_task_invalid_billing_account(self):
        task = service_activation_task.apply(["11", "sddsd"])
        self.assertEqual(task.status, celery.states.FAILURE)

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    def test_service_activation_task_fraud_billing_account(self):
        billing_account = BillingAccountFactory.create(verification_state=5)
        ban_id = billing_account.id
        payment_id = "456"
        task = service_activation_task.apply([ban_id, payment_id])
        self.assertEqual(task.status, celery.states.FAILURE)

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    def test_service_activation_task_invalid_service(self):
        billing_account = BillingAccountFactory.create()
        ban_id = billing_account.id
        payment_id = "456"
        task = service_activation_task.apply([ban_id, payment_id])
        self.assertEqual(task.status, celery.states.FAILURE)

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    def test_service_activation_task_not_a_demo_service(self):
        recharge = RechargesFactory.create(
            payment_id="abc_333", request_of="activation"
        )
        ServiceFactory.create(
            billing_account=recharge.billing_account, live_status=1
        )
        ban_id = recharge.billing_account.id
        payment_id = recharge.payment_id
        task = service_activation_task.apply([ban_id, payment_id])
        self.assertEqual(task.status, celery.states.FAILURE)

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    def test_service_activation_task_payment_not_settled(self):
        recharge = RechargesFactory.create(
            payment_id="abc_33322", request_of="activation"
        )
        ServiceFactory.create(
            billing_account=recharge.billing_account, status=0
        )
        ban_id = recharge.billing_account.id
        payment_id = recharge.payment_id
        task = service_activation_task.apply([ban_id, payment_id])
        self.assertEqual(task.status, celery.states.FAILURE)

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @mock.patch(
        "accounts.utils.api_services.account_v1.AccountApiV1.initiate_activation"
    )
    def test_service_activation_task_failed_activation(
        self, mock_initiate_activation
    ):
        mock_initiate_activation.return_value = False
        ban_id = self.recharge.billing_account.id
        payment_id = self.recharge.payment_id
        task = service_activation_task.apply([ban_id, payment_id])
        self.assertEqual(task.status, celery.states.FAILURE)
