"""
With these settings, tests run faster.
"""

from fakeredis import FakeConnection
from .base import *  # noqa
from .base import env

# GENERAL
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#secret-key
SECRET_KEY = env(
    "DJANGO_SECRET_KEY",
    default="MOKtr9tQC4B8gmZH1Q9LrBFHfRfBfHC93fVKvImK33Q0NFFZ311KhntispN5P0tT",
)
# https://docs.djangoproject.com/en/dev/ref/settings/#test-runner
TEST_RUNNER = "django.test.runner.DiscoverRunner"
# CACHES
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#caches
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://test",
        "KEY_PREFIX": "accounts",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "CONNECTION_POOL_KWARGS": {"connection_class": FakeConnection},
        },
    }
}


REST_FRAMEWORK["DEFAULT_AUTHENTICATION_CLASSES"] = []

# Your stuff...
# ------------------------------------------------------------------------------

CELERY_TASK_ALWAYS_EAGER = True
CELERY_TASK_EAGER_PROPAGATES = True
